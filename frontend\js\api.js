/**
 * Module pour les appels API vers le backend
 */

const API = {
    /**
     * Effectue une requête fetch avec gestion des erreurs
     * @param {string} url - URL de la requête
     * @param {Object} options - Options de la requête fetch
     * @returns {Promise<Object>} - Réponse JSON
     */
    async fetchWithErrorHandling(url, options = {}) {
        try {
            const response = await fetch(url, options);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Une erreur est survenue');
            }

            return data;
        } catch (error) {
            console.error('Erreur API:', error);
            throw error;
        }
    },

    /**
     * Récupère les listes de lecture de l'utilisateur
     * @returns {Promise<Object>} - Réponse JSON avec les listes
     */
    async getReadingLists() {
        return this.fetchWithErrorHandling('/api/reading-lists');
    },

    /**
     * Récupère les listes de lecture avec le statut d'un livre spécifique
     * @param {number} bookId - ID du livre
     * @returns {Promise<Object>} - Réponse JSON avec les listes et leur statut
     */
    async getReadingListsWithBookStatus(bookId) {
        return this.fetchWithErrorHandling(`/api/reading-lists/book-status/${bookId}`);
    },

    /**
     * Récupère une liste de lecture spécifique avec ses livres
     * @param {number} listId - ID de la liste
     * @param {string} search - Terme de recherche (optionnel)
     * @param {string} sort - Option de tri (optionnel)
     * @returns {Promise<Object>} - Réponse JSON avec la liste et ses livres
     */
    async getReadingList(listId, search = '', sort = 'title_asc') {
        let url = `/api/reading-lists/${listId}`;
        const params = new URLSearchParams();

        if (search) {
            params.append('search', search);
        }

        if (sort) {
            params.append('sort', sort);
        }

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        return this.fetchWithErrorHandling(url);
    },

    /**
     * Crée une nouvelle liste de lecture
     * @param {string} listName - Nom de la liste
     * @param {string} description - Description de la liste (optionnel)
     * @returns {Promise<Object>} - Réponse JSON avec la liste créée
     */
    async createReadingList(listName, description = '') {
        // Utiliser un formulaire pour envoyer les données comme le fait le backend
        const formData = new URLSearchParams();
        formData.append('list_name', listName);
        formData.append('description', description);
        formData.append('is_public', 'false');

        const response = await fetch('/dashboard/reading-lists', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData.toString()
        });

        if (response.redirected) {
            // Si la réponse est une redirection, c'est un succès
            return { success: true };
        }

        throw new Error('Erreur lors de la création de la liste');
    },

    /**
     * Met à jour le nom d'une liste de lecture
     * @param {number} listId - ID de la liste
     * @param {string} listName - Nouveau nom de la liste
     * @returns {Promise<Object>} - Réponse JSON avec la liste mise à jour
     */
    async updateReadingList(listId, listName) {
        return this.fetchWithErrorHandling(`/api/reading-lists/${listId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ list_name: listName })
        });
    },

    /**
     * Supprime une liste de lecture
     * @param {number} listId - ID de la liste
     * @returns {Promise<Object>} - Réponse JSON avec message de succès
     */
    async deleteReadingList(listId) {
        return this.fetchWithErrorHandling(`/api/reading-lists/${listId}`, {
            method: 'DELETE'
        });
    },

    /**
     * Ajoute un livre à une liste de lecture
     * @param {number} listId - ID de la liste
     * @param {number} bookId - ID du livre
     * @returns {Promise<Object>} - Réponse JSON avec message de succès
     */
    async addBookToList(listId, bookId) {
        return this.fetchWithErrorHandling('/api/reading-lists/add-book', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ list_id: listId, book_id: bookId })
        });
    },

    /**
     * Supprime un livre d'une liste de lecture
     * @param {number} listId - ID de la liste
     * @param {number} bookId - ID du livre
     * @returns {Promise<Object>} - Réponse JSON avec message de succès
     */
    async removeBookFromList(listId, bookId) {
        return this.fetchWithErrorHandling(`/api/reading-lists/${listId}/books/${bookId}`, {
            method: 'DELETE'
        });
    },

    /**
     * Récupère les favoris de l'utilisateur
     * @param {string} search - Terme de recherche (optionnel)
     * @param {string} sort - Option de tri (optionnel)
     * @returns {Promise<Object>} - Réponse JSON avec les favoris
     */
    async getFavorites(search = '', sort = 'title_asc') {
        let url = '/api/favorites';
        const params = new URLSearchParams();

        if (search) {
            params.append('search', search);
        }

        if (sort) {
            params.append('sort', sort);
        }

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        return this.fetchWithErrorHandling(url);
    },

    /**
     * Vérifie si un livre spécifique est dans les favoris
     * @param {number} bookId - ID du livre
     * @returns {Promise<Object>} - Réponse JSON avec le statut favori
     */
    async checkFavoriteStatus(bookId) {
        return this.fetchWithErrorHandling(`/api/favorites/check/${bookId}`);
    },

    /**
     * Ajoute un livre aux favoris
     * @param {number} bookId - ID du livre
     * @returns {Promise<Object>} - Réponse JSON avec message de succès
     */
    async addFavorite(bookId) {
        return this.fetchWithErrorHandling('/api/favorites', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ book_id: bookId })
        });
    },

    /**
     * Supprime un livre des favoris
     * @param {number} bookId - ID du livre
     * @returns {Promise<Object>} - Réponse JSON avec message de succès
     */
    async removeFavorite(bookId) {
        return this.fetchWithErrorHandling(`/api/favorites/${bookId}`, {
            method: 'DELETE'
        });
    },

    /**
     * Récupère les avis pour un livre
     * @param {number} bookId - ID du livre
     * @param {Object} options - Options de filtrage et pagination
     * @returns {Promise<Object>} - Réponse JSON avec les avis
     */
    async getBookReviews(bookId, options = {}) {
        const {
            page = 1,
            limit = 10,
            orderBy = 'created_at',
            order = 'DESC',
            minRating = null,
            maxRating = null,
            hasComment = false
        } = options;

        let url = `/api/books/${bookId}/reviews`;
        const params = new URLSearchParams();

        params.append('page', page);
        params.append('limit', limit);
        params.append('orderBy', orderBy);
        params.append('order', order);

        if (minRating !== null) {
            params.append('minRating', minRating);
        }

        if (maxRating !== null) {
            params.append('maxRating', maxRating);
        }

        if (hasComment) {
            params.append('hasComment', 'true');
        }

        url += `?${params.toString()}`;

        return this.fetchWithErrorHandling(url);
    },

    /**
     * Récupère les avis de l'utilisateur
     * @param {string} search - Terme de recherche (optionnel)
     * @param {string} sort - Option de tri (optionnel)
     * @param {number} minRating - Note minimale (optionnel)
     * @returns {Promise<Object>} - Réponse JSON avec les avis
     */
    async getUserReviews(search = '', sort = 'date_desc', minRating = 0) {
        let url = '/api/user/reviews';
        const params = new URLSearchParams();

        if (search) {
            params.append('search', search);
        }

        if (sort) {
            params.append('sort', sort);
        }

        if (minRating > 0) {
            params.append('minRating', minRating);
        }

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        return this.fetchWithErrorHandling(url);
    },

    /**
     * Crée un nouvel avis
     * @param {number} bookId - ID du livre
     * @param {number} rating - Note (1-5)
     * @param {string} comment - Commentaire (optionnel)
     * @returns {Promise<Object>} - Réponse JSON avec l'avis créé
     */
    async createReview(bookId, rating, comment) {
        return this.fetchWithErrorHandling('/api/reviews', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ book_id: bookId, rating, comment })
        });
    },

    /**
     * Met à jour un avis existant
     * @param {number} reviewId - ID de l'avis
     * @param {number} rating - Note (1-5)
     * @param {string} comment - Commentaire (optionnel)
     * @returns {Promise<Object>} - Réponse JSON avec l'avis mis à jour
     */
    async updateReview(reviewId, rating, comment) {
        return this.fetchWithErrorHandling(`/api/reviews/${reviewId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ rating, comment })
        });
    },

    /**
     * Supprime un avis
     * @param {number} reviewId - ID de l'avis
     * @returns {Promise<Object>} - Réponse JSON avec message de succès
     */
    async deleteReview(reviewId) {
        return this.fetchWithErrorHandling(`/api/reviews/${reviewId}`, {
            method: 'DELETE'
        });
    }
};

export default API;
