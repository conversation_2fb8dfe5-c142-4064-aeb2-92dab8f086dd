
/**
 * Configuration de l'application Express
 * Ce module configure l'application Express avec tous les middlewares nécessaires
 */

import express from 'express';
import session from 'express-session';
import ConnectSqlite3 from 'connect-sqlite3';
import passport from 'passport';
import flash from 'express-flash';
import fileUpload from 'express-fileupload';
import path from 'path';
import { fileURLToPath } from 'url';
import { ENV } from './env.js';
import fs from 'fs';

// --- Calculer __dirname ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Configure l'application Express
 * @param {Object} options - Options de configuration
 * @returns {Object} - L'application Express configurée
 */
export const configureExpress = (options = {}) => {
  const app = express();

  // Middleware pour parser le corps des requêtes
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Middleware pour gérer les uploads de fichiers
  app.use(fileUpload({
    createParentPath: true, // Crée automatiquement les dossiers parents si nécessaire
    limits: {
      fileSize: 10 * 1024 * 1024 // Limite de taille de fichier à 10MB
    },
    abortOnLimit: true, // Annule l'upload si la limite est dépassée
    useTempFiles: true, // Utilise des fichiers temporaires pour éviter de surcharger la mémoire
    tempFileDir: path.join(process.cwd(), 'tmp') // Dossier pour les fichiers temporaires
  }));

  // Déterminer le chemin de la base de données pour les sessions
  const DB_PATH = ENV.DATABASE_PATH;
  const dbPath = path.isAbsolute(DB_PATH)
    ? DB_PATH
    : path.join(process.cwd(), DB_PATH);

  // Vérifier si le répertoire existe, sinon le créer
  const dbDir = path.dirname(dbPath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }

  // Configuration du stockage de session SQLite
  const SQLiteStore = ConnectSqlite3(session);
  const sessionStore = new SQLiteStore({
    db: path.basename(dbPath),
    dir: path.dirname(dbPath)
  });

  console.log(`[SESSION] Stockage des sessions dans: ${dbPath}`);

  // Configuration de la session
  app.use(session({
    store: sessionStore,
    secret: ENV.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
      maxAge: 1000 * 60 * 60 * 24, // 1 jour
      httpOnly: true
    }
  }));

  // Configuration de Flash pour les messages
  app.use(flash());

  // Initialisation de Passport
  app.use(passport.initialize());
  app.use(passport.session());

  // Middleware pour rendre l'utilisateur disponible dans toutes les vues
  app.use((req, res, next) => {
    res.locals.currentUser = req.user;
    res.locals.success = req.flash('success');
    res.locals.error = req.flash('error');
    next();
  });

  // Configuration du moteur de template EJS
  app.set('view engine', 'ejs');
  app.set('views', path.join(__dirname, '..', 'views'));

  // Configuration des fichiers statiques
  app.use(express.static(path.join(__dirname, '..', '..', 'frontend')));
  // Servir les fichiers uploadés depuis le dossier public
  app.use('/uploads', express.static(path.join(__dirname, '..', '..', 'public', 'uploads')));

  return app;
};

/**
 * Configure les routes de l'application
 * @param {Object} app - L'application Express
 * @param {Object} routes - Les routes à configurer
 * @param {Object} sequelize - L'instance Sequelize
 */
export const configureRoutes = (app, routes, sequelize) => {
  const { homeRoutes, authRoutes, adminRoutes, bookRoutes, readerRoutes, apiRoutes, reviewRoutes } = routes;

  // Routes de la page d'accueil
  app.use('/', homeRoutes);

  // Routes d'authentification
  app.use('/', authRoutes);

  // Routes d'administration
  app.use('/admin', adminRoutes);

  // Routes des livres
  app.use('/books', bookRoutes);

  // Routes pour les fonctionnalités de lecteur (changé de /api à /dashboard)
  app.use('/dashboard', readerRoutes);

  // Routes API pour les fonctionnalités de lecteur
  app.use('/api', apiRoutes);

  // Routes API pour les avis
  app.use('/api', reviewRoutes);

  // Route pour tester la connexion à la base de données
  app.get('/api/test-db', async (_, res) => {
    try {
      await sequelize.authenticate();
      res.json({
        message: 'Connexion à la base de données réussie',
        database_path: sequelize.options.storage
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Middleware pour gérer les routes non trouvées
  app.use((_, res) => {
    res.status(404).render('error', {
      title: 'Page non trouvée - Nookli',
      message: 'La page que vous recherchez n\'existe pas.',
      statusCode: 404
    });
  });

  // Middleware pour gérer les erreurs
  app.use((err, _, res, __) => {
    console.error(err.stack);
    res.status(500).render('error', {
      title: 'Erreur - Nookli',
      message: 'Une erreur est survenue sur le serveur.',
      statusCode: 500
    });
  });
};

export default {
  configureExpress,
  configureRoutes
};

