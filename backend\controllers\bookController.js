import { Book, Genre, Favorite } from '../models/index.js';

/**
 * Affiche la liste de tous les livres avec options de filtrage et tri
 */
export async function showAllBooks(req, res) {
    try {
        // Récupérer les paramètres de requête pour le tri et le filtrage
        const {
            sort = 'title',
            order = 'ASC',
            page = 1,
            limit = 12,
            genre_id,
            min_rating,
            year
        } = req.query;

        // Calculer l'offset pour la pagination
        const offset = (page - 1) * limit;

        // Options de base pour la requête
        const options = {
            limit: parseInt(limit),
            offset: parseInt(offset),
            orderBy: sort,
            order: order.toUpperCase()
        };

        // Ajouter les filtres si présents
        const filters = {};
        if (genre_id) filters.genre_id = genre_id;
        if (min_rating) filters.min_rating = parseFloat(min_rating);
        if (year) filters.publication_year = parseInt(year);

        // Récupérer tous les genres pour le filtrage
        const genres = await Genre.findAll();

        // Récupérer les livres avec les filtres (seulement les publiés pour le frontend)
        const { books, total } = await Book.findFiltered(filters, options);
        const totalPages = Math.ceil(total / limit);

        res.render('books/index', {
            title: 'Catalogue de livres - Nookli',
            currentUser: req.user,
            books,
            genres,
            currentGenre: null, // Ajout de currentGenre avec la valeur null
            currentPage: parseInt(page),
            totalPages,
            totalBooks: total,
            sort,
            order,
            genre_id: genre_id || '',
            min_rating: min_rating || '',
            year: year || ''
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des livres:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des livres.');
        res.redirect('/');
    }
}

/**
 * Affiche les détails d'un livre spécifique
 */
export async function showBookDetails(req, res) {
    try {
        const bookId = req.params.id;

        // Pour les détails d'un livre, on ne montre que les livres publiés aux utilisateurs normaux
        const includeAllStatuses = req.user && req.user.role === 'admin';

        // Utiliser findOne avec les options d'inclusion pour obtenir le genre
        const whereConditions = { id: bookId };
        if (!includeAllStatuses) {
            whereConditions.status = 'published';
        }

        const book = await Book.findOne({
            where: whereConditions,
            include: [{ model: Genre, as: 'genre' }]
        });

        if (!book) {
            req.flash('error', 'Le livre demandé n\'existe pas ou n\'est pas publié.');
            return res.redirect('/books');
        }

        // Calculer le rating à afficher (combinaison de average_rating et initial_rating)
        let displayRating = 0;
        let reviewCount = 0;

        // Si il y a un average_rating (calculé à partir des avis utilisateurs), l'utiliser
        if (book.average_rating && book.average_rating > 0) {
            displayRating = book.average_rating;

            // Compter le nombre d'avis pour ce livre
            const { Review } = await import('../models/index.js');
            reviewCount = await Review.count({
                where: { book_id: bookId }
            });
        }
        // Sinon, utiliser l'initial_rating si disponible
        else if (book.initial_rating && book.initial_rating > 0) {
            displayRating = book.initial_rating;
            reviewCount = 0; // Pas d'avis utilisateurs, juste le rating initial
        }

        // Vérifier si le livre est dans les favoris de l'utilisateur connecté
        let isFavorite = false;
        let userReview = null;
        let hasUserReviewed = false;

        if (req.user) {
            try {
                isFavorite = await Favorite.isFavorite(req.user.id, bookId);

                // Vérifier si l'utilisateur a déjà donné un avis sur ce livre
                const { Review } = await import('../models/index.js');
                userReview = await Review.findOne({
                    where: {
                        user_id: req.user.id,
                        book_id: bookId
                    }
                });
                hasUserReviewed = !!userReview;
            } catch (error) {
                console.error('Erreur lors de la vérification du statut favori/avis:', error);
                // En cas d'erreur, on continue avec les valeurs par défaut
            }
        }

        res.render('books/details', {
            title: `${book.title} - Nookli`,
            currentUser: req.user,
            book,
            displayRating: displayRating.toFixed(1),
            reviewCount,
            isFavorite,
            hasUserReviewed,
            userReview
        });
    } catch (err) {
        console.error('Erreur lors de la récupération du livre:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement du livre.');
        res.redirect('/books');
    }
}

/**
 * Recherche des livres par titre ou auteur
 */
export async function searchBooks(req, res) {
    try {
        // Récupérer les paramètres de requête
        const {
            q,
            sort = 'title',
            order = 'ASC',
            page = 1,
            limit = 12,
            genre_id,
            min_rating,
            year
        } = req.query;

        // Vérifier si une requête de recherche est présente
        if (!q || q.trim() === '') {
            return res.redirect('/books');
        }

        // Calculer l'offset pour la pagination
        const offset = (page - 1) * limit;

        // Options de base pour la requête
        const options = {
            limit: parseInt(limit),
            offset: parseInt(offset),
            orderBy: sort,
            order: order.toUpperCase()
        };

        // Ajouter les filtres si présents
        const filters = {};
        if (genre_id) filters.genre_id = genre_id;
        if (min_rating) filters.min_rating = parseFloat(min_rating);
        if (year) filters.publication_year = parseInt(year);

        // Récupérer tous les genres pour le filtrage
        const genres = await Genre.findAll();

        // Rechercher les livres avec le terme de recherche et les filtres (seulement les publiés pour le frontend)
        const { books, total } = await Book.search(q, filters, options);
        const totalPages = Math.ceil(total / limit);

        res.render('books/search', {
            title: `Recherche: ${q} - Nookli`,
            currentUser: req.user,
            books,
            genres,
            currentGenre: null, // Ajout de currentGenre avec la valeur null
            searchQuery: q,
            currentPage: parseInt(page),
            totalPages,
            totalBooks: total,
            sort,
            order,
            genre_id: genre_id || '',
            min_rating: min_rating || '',
            year: year || ''
        });
    } catch (err) {
        console.error('Erreur lors de la recherche de livres:', err);
        req.flash('error', 'Une erreur est survenue lors de la recherche.');
        res.redirect('/books');
    }
}

/**
 * Affiche les livres filtrés par genre
 */
export async function showBooksByGenre(req, res) {
    try {
        const genreId = req.params.id;
        const {
            sort = 'title',
            order = 'ASC',
            page = 1,
            limit = 12,
            min_rating,
            year
        } = req.query;

        // Calculer l'offset pour la pagination
        const offset = (page - 1) * limit;

        // Options de base pour la requête
        const options = {
            limit: parseInt(limit),
            offset: parseInt(offset),
            orderBy: sort,
            order: order.toUpperCase()
        };

        // Ajouter les filtres
        const filters = { genre_id: genreId };
        if (min_rating) filters.min_rating = parseFloat(min_rating);
        if (year) filters.publication_year = parseInt(year);

        // Récupérer le genre et tous les genres pour le filtrage
        const [genre, genres] = await Promise.all([
            Genre.findByPk(genreId),
            Genre.findAll()
        ]);

        if (!genre) {
            req.flash('error', 'Le genre demandé n\'existe pas.');
            return res.redirect('/books');
        }

        // Récupérer les livres du genre (seulement les publiés pour le frontend)
        const { books, total } = await Book.findFiltered(filters, options);
        const totalPages = Math.ceil(total / limit);

        res.render('books/index', {
            title: `Livres de ${genre.name} - Nookli`,
            currentUser: req.user,
            books,
            genres,
            currentGenre: genre,
            currentPage: parseInt(page),
            totalPages,
            totalBooks: total,
            sort,
            order,
            genre_id: genreId,
            min_rating: min_rating || '',
            year: year || ''
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des livres par genre:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des livres.');
        res.redirect('/books');
    }
}

// Exporter toutes les fonctions
export default {
    showAllBooks,
    showBookDetails,
    searchBooks,
    showBooksByGenre
};
