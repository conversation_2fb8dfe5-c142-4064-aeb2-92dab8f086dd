/**
 * Script pour recalculer tous les ratings des livres
 * en incluant l'initial_rating dans le calcul
 */

import sequelize from '../config/db.js';
import Book from '../models/Book.js';
import '../models/index.js'; // Pour charger les associations

async function recalculateAllRatings() {
    try {
        console.log('🔄 Début du recalcul des ratings...');
        
        // Récupérer tous les livres
        const books = await Book.findAll();
        console.log(`📚 ${books.length} livres trouvés`);
        
        let updated = 0;
        
        for (const book of books) {
            console.log(`\n📖 Traitement du livre: "${book.title}"`);
            console.log(`   Initial rating: ${book.initial_rating || 'aucun'}`);
            console.log(`   Average rating actuel: ${book.average_rating || 'aucun'}`);
            
            // Utiliser la méthode updateAverageRating qui inclut maintenant l'initial_rating
            const updatedBook = await Book.updateAverageRating(book.id);
            
            console.log(`   ✅ Nouveau average rating: ${updatedBook.average_rating}`);
            updated++;
        }
        
        console.log(`\n🎉 Recalcul terminé ! ${updated} livres mis à jour.`);
        
    } catch (error) {
        console.error('❌ Erreur lors du recalcul des ratings:', error);
    } finally {
        await sequelize.close();
    }
}

// Exécuter le script
recalculateAllRatings();
