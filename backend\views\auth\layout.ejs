<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof title !== 'undefined' ? title : 'Mon Espace - Nookli' %></title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/books.css">
    <link rel="stylesheet" href="/css/reader-features.css">
    <link rel="stylesheet" href="/css/reader-dashboard.css">
    <link rel="stylesheet" href="/css/reading-lists.css">
    <!-- Icônes Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="reader-body">
    <header class="reader-header">
        <div class="container">
            <nav>
                <a class="brand" href="/">
                    <i class="fas fa-book-reader"></i> Nookli
                </a>

                <ul class="nav-links">
                    <li><a href="/">Accueil</a></li>
                    <li><a href="/books">Catalogue</a></li>
                    <li>
                        <form action="/logout" method="POST" style="display: inline;">
                            <button type="submit" class="logout-button">Déconnexion</button>
                        </form>
                    </li>
                </ul>

                <!-- Bouton menu mobile -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Menu">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <div class="reader-container">
        <aside class="reader-sidebar">
            <button class="reader-sidebar-toggle" id="sidebar-toggle" title="Réduire/Agrandir le menu">
                <i class="fas fa-chevron-left"></i>
            </button>

            <div class="reader-user">
                <div class="reader-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="reader-user-info">
                    <p class="reader-username"><%= currentUser.username %></p>
                    <p class="reader-role">Lecteur</p>
                </div>
            </div>

            <nav class="reader-nav">
                <ul>
                    <li>
                        <a href="/dashboard" class="<%= path === '/dashboard' ? 'active' : '' %>">
                            <i class="fas fa-tachometer-alt"></i> <span class="nav-text">Tableau de bord</span>
                        </a>
                    </li>
                    <li>
                        <a href="/dashboard/reading-lists" class="<%= path === '/dashboard/reading-lists' ? 'active' : '' %>">
                            <i class="fas fa-list"></i> <span class="nav-text">Mes listes de lecture</span>
                        </a>
                    </li>
                    <li>
                        <a href="/dashboard/favorites" class="<%= path === '/dashboard/favorites' ? 'active' : '' %>">
                            <i class="fas fa-heart"></i> <span class="nav-text">Mes favoris</span>
                        </a>
                    </li>
                    <li>
                        <a href="/dashboard/reviews" class="<%= path === '/dashboard/reviews' ? 'active' : '' %>">
                            <i class="fas fa-star"></i> <span class="nav-text">Mes avis</span>
                        </a>
                    </li>
                    <li>
                        <a href="/profile" class="<%= path === '/profile' ? 'active' : '' %>">
                            <i class="fas fa-user"></i> <span class="nav-text">Mon profil</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="reader-main">
            <%- include('../partials/flash-messages') %>

            <%- body %>
        </main>
    </div>

    <footer class="reader-footer">
        <div class="container">
            <p>© <%= new Date().getFullYear() %> Nookli - Tous droits réservés</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js" type="module"></script>
    <script src="/js/books.js"></script>
    <script src="/js/reader-features.js"></script>
</body>
</html>

