/**
 * Point d'entrée principal de l'application Nookli
 * Ce fichier configure et démarre le serveur
 */

// Importer la configuration des variables d'environnement en premier
import { ENV } from './config/env.js';

// Importer les modèles et la configuration
import models from './models/index.js';
import configurePassport from './config/passport.js';
import { configureExpress, configureRoutes } from './config/express.js';
import { initServer, startServer } from './utils/serverManager.js';

// Importer les routes
import authRoutes from './routes/authRoutes.js';
import adminRoutes from './routes/adminRoutes.js';
import bookRoutes from './routes/bookRoutes.js';
import homeRoutes from './routes/homeRoutes.js';
import readerRoutes from './routes/readerRoutes.js';
import apiRoutes from './routes/apiRoutes.js';
import reviewRoutes from './routes/reviewRoutes.js';

// Définition du Port
const PORT = ENV.PORT;

/**
 * Fonction principale pour démarrer l'application
 */
async function bootstrap() {
  try {
    // Initialiser le serveur (base de données et admin)
    const initialized = await initServer(models);
    if (!initialized) {
      console.error('[APP] Erreur lors de l\'initialisation du serveur.');
      process.exit(1);
    }

    // Configurer Passport
    configurePassport();

    // Configurer l'application Express
    const app = configureExpress();

    // Configurer les routes
    configureRoutes(app, {
      homeRoutes,
      authRoutes,
      adminRoutes,
      bookRoutes,
      readerRoutes,
      apiRoutes,
      reviewRoutes
    }, models.sequelize);

    // Démarrer le serveur
    await startServer(app, PORT);
  } catch (error) {
    console.error('[APP] Erreur fatale lors du démarrage de l\'application:', error);
    process.exit(1);
  }
}

// Démarrer l'application
bootstrap();
