/**
 * Module pour la gestion des favoris
 */
import API from '../api.js';

const FavoritesManager = {
    /**
     * Initialise le gestionnaire de favoris
     */
    init() {
        // Éléments DOM pour le dashboard
        this.favoritesContainer = document.querySelector('.favorites-container');
        if (this.favoritesContainer) {
            this.initFavoritesActions();
        }

        // Éléments DOM pour la page de détails du livre
        this.bookDetailsPage = document.querySelector('.book-details');
        if (this.bookDetailsPage) {
            this.initBookDetailsPage();
        }
    },

    /**
     * Initialise les actions sur les favoris dans le dashboard
     */
    initFavoritesActions() {
        // Boutons pour retirer un livre des favoris
        const removeButtons = document.querySelectorAll('.remove-favorite');
        removeButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const bookId = button.dataset.bookId;
                await this.removeFavorite(bookId);
            });
        });

        // Initialiser les filtres et la recherche
        this.initFilters();
    },

    /**
     * Initialise les filtres et la recherche pour les favoris
     */
    initFilters() {
        const searchInput = document.getElementById('search-favorites');
        const searchBtn = document.getElementById('search-favorites-btn');
        const sortSelect = document.getElementById('sort-favorites');
        const applyBtn = document.getElementById('apply-favorites-filters');

        if (searchBtn && applyBtn) {
            // Recherche au clic sur le bouton
            searchBtn.addEventListener('click', () => {
                this.applyFilters();
            });

            // Recherche en appuyant sur Entrée
            searchInput?.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });

            // Appliquer les filtres au clic sur le bouton
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
            });

            // Appliquer les filtres au changement de tri
            sortSelect?.addEventListener('change', () => {
                this.applyFilters();
            });
        }
    },

    /**
     * Applique les filtres et la recherche aux favoris
     */
    async applyFilters() {
        const searchInput = document.getElementById('search-favorites');
        const sortSelect = document.getElementById('sort-favorites');

        if (!searchInput || !sortSelect || !this.favoritesContainer) return;

        const search = searchInput.value.trim();
        const sort = sortSelect.value;

        try {
            // Afficher un indicateur de chargement
            this.favoritesContainer.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> Chargement...
                </div>
            `;

            // Récupérer les favoris filtrés
            const response = await API.getFavorites(search, sort);
            const favorites = response.data;

            // Mettre à jour l'affichage
            if (favorites && favorites.length > 0) {
                this.favoritesContainer.innerHTML = `
                    <div class="filter-result-message">${favorites.length} livre(s) trouvé(s)</div>
                    <div class="books-grid">
                        ${favorites.map(book => this.generateBookCardHTML(book)).join('')}
                    </div>
                `;

                // Réinitialiser les événements
                this.initFavoritesActions();
            } else {
                this.favoritesContainer.innerHTML = `
                    <div class="empty-state">
                        <p>Aucun livre ne correspond à votre recherche.</p>
                        <button class="btn btn-secondary reset-filters" id="reset-favorites-filters">Réinitialiser les filtres</button>
                    </div>
                `;

                // Ajouter un événement pour réinitialiser les filtres
                const resetBtn = document.getElementById('reset-favorites-filters');
                if (resetBtn) {
                    resetBtn.addEventListener('click', () => {
                        searchInput.value = '';
                        sortSelect.value = 'title_asc';
                        this.applyFilters();
                    });
                }
            }
        } catch (error) {
            console.error('Erreur lors de l\'application des filtres:', error);
            this.favoritesContainer.innerHTML = `
                <div class="error-message">
                    <p>Une erreur est survenue lors du chargement des favoris.</p>
                    <button class="btn btn-secondary" id="retry-favorites">Réessayer</button>
                </div>
            `;

            // Ajouter un événement pour réessayer
            const retryBtn = document.getElementById('retry-favorites');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    this.applyFilters();
                });
            }
        }
    },

    /**
     * Génère le HTML pour une carte de livre
     * @param {Object} book - Objet livre
     * @returns {string} - HTML de la carte
     */
    generateBookCardHTML(book) {
        return `
            <div class="book-card">
                <a href="/books/${book.id}" class="book-link">
                    <div class="book-cover">
                        <img src="${book.cover_image_url || 'https://via.placeholder.com/300x450?text=Nookli'}" alt="Couverture de ${book.title}">
                    </div>
                    <div class="book-info">
                        <h4>${book.title}</h4>
                        <p class="book-author">${book.author}</p>
                    </div>
                </a>
                <div class="book-actions">
                    <button class="btn btn-text remove-favorite" data-book-id="${book.id}">
                        <i class="fas fa-heart-broken"></i> Retirer des favoris
                    </button>
                    <button class="btn btn-text add-to-list-btn" data-book-id="${book.id}">
                        <i class="fas fa-list"></i> Ajouter à une liste
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * Initialise les actions sur la page de détails du livre
     */
    initBookDetailsPage() {
        const bookId = this.getBookIdFromUrl();
        if (!bookId) return;

        // Bouton pour ajouter/retirer des favoris
        const favoriteBtn = document.getElementById('favoriteBtn');
        if (favoriteBtn) {
            // Le bouton est maintenant rendu avec le bon état initial depuis le serveur
            // Pas besoin de vérifier à nouveau le statut au chargement
            console.log('Bouton favori initialisé avec état:', favoriteBtn.classList.contains('active') ? 'favori' : 'non favori');

            favoriteBtn.addEventListener('click', async (e) => {
                // Empêcher les clics multiples
                if (favoriteBtn.disabled) return;

                favoriteBtn.disabled = true;
                const originalText = favoriteBtn.innerHTML;
                favoriteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Traitement...';

                try {
                    const isFavorite = favoriteBtn.classList.contains('active');
                    if (isFavorite) {
                        await this.removeFavorite(bookId);
                    } else {
                        await this.addFavorite(bookId);
                    }
                    this.updateFavoriteButton(favoriteBtn, !isFavorite);
                } catch (error) {
                    console.error('Erreur lors de la gestion du favori:', error);
                    // Restaurer l'état original en cas d'erreur
                    favoriteBtn.innerHTML = originalText;
                } finally {
                    favoriteBtn.disabled = false;
                }
            });
        }
    },

    /**
     * Vérifie si l'utilisateur est connecté
     * @returns {boolean} - true si l'utilisateur est connecté, false sinon
     */
    isUserLoggedIn() {
        // Vérifier la présence d'éléments qui indiquent que l'utilisateur est connecté
        const hasUserActions = document.querySelector('.book-actions') !== null;
        const hasGuestActions = document.querySelector('.book-actions-guest') !== null;

        console.log('Vérification connexion:', { hasUserActions, hasGuestActions });

        // L'utilisateur est connecté s'il y a des actions utilisateur ET pas d'actions invité
        return hasUserActions && !hasGuestActions;
    },

    /**
     * Récupère l'ID du livre depuis l'URL
     * @returns {number|null} - ID du livre ou null
     */
    getBookIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/books\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    },

    /**
     * Vérifie si un livre spécifique est dans les favoris (nouvelle API efficace)
     * @param {number} bookId - ID du livre
     * @returns {Promise<boolean>} - true si le livre est dans les favoris, false sinon
     */
    async checkIfFavorite(bookId) {
        try {
            const response = await API.checkFavoriteStatus(bookId);
            return response.isFavorite;
        } catch (error) {
            console.error('Erreur lors de la vérification du statut favori:', error);
            return false;
        }
    },

    /**
     * Synchronise le statut du bouton favori avec le serveur (optionnel)
     * @param {number} bookId - ID du livre
     * @param {HTMLElement} button - Bouton à synchroniser
     */
    async syncFavoriteStatus(bookId, button) {
        try {
            const isFavorite = await this.checkIfFavorite(bookId);
            const currentState = button.classList.contains('active');

            // Mettre à jour seulement si l'état diffère
            if (isFavorite !== currentState) {
                console.log('Synchronisation nécessaire - État serveur:', isFavorite, 'État client:', currentState);
                this.updateFavoriteButton(button, isFavorite);
            }
        } catch (error) {
            console.error('Erreur lors de la synchronisation du statut favori:', error);
        }
    },

    /**
     * Met à jour l'apparence du bouton favori
     * @param {HTMLElement} button - Bouton à mettre à jour
     * @param {boolean} isFavorite - true si le livre est dans les favoris, false sinon
     */
    updateFavoriteButton(button, isFavorite) {
        console.log('Mise à jour du bouton favori:', { isFavorite, buttonId: button.id });

        if (isFavorite) {
            button.classList.add('active');
            button.innerHTML = '<i class="fas fa-heart"></i> Retirer des favoris';
            console.log('Bouton mis en mode actif (favori)');
        } else {
            button.classList.remove('active');
            button.innerHTML = '<i class="far fa-heart"></i> Ajouter aux favoris';
            console.log('Bouton mis en mode inactif (non favori)');
        }

        // Vérifier que les classes sont bien appliquées
        console.log('Classes du bouton après mise à jour:', button.className);
    },

    /**
     * Ajoute un livre aux favoris
     * @param {number} bookId - ID du livre
     */
    async addFavorite(bookId) {
        try {
            await API.addFavorite(bookId);
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    },

    /**
     * Retire un livre des favoris
     * @param {number} bookId - ID du livre
     */
    async removeFavorite(bookId) {
        try {
            await API.removeFavorite(bookId);

            // Si nous sommes sur la page des favoris, supprimer l'élément du DOM
            if (this.favoritesContainer) {
                const bookCard = document.querySelector(`.book-card [data-book-id="${bookId}"]`).closest('.book-card');
                if (bookCard) {
                    bookCard.remove();

                    // Vérifier s'il reste des favoris
                    const remainingCards = this.favoritesContainer.querySelectorAll('.book-card');
                    if (remainingCards.length === 0) {
                        this.favoritesContainer.innerHTML = `
                            <div class="empty-state">
                                <p>Vous n'avez pas encore de livres favoris.</p>
                                <a href="/books" class="btn btn-secondary">Explorer le catalogue</a>
                            </div>
                        `;
                    }
                }
            }
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    }
};

export default FavoritesManager;
