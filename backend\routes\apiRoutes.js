import express from 'express';
import * as readerController from '../controllers/readerController.js';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// Middleware d'authentification pour toutes les routes API
router.use(isAuthenticated);

// Routes API pour les listes de lecture
router.get('/reading-lists', readerController.getReadingListsAPI);
router.get('/reading-lists/book-status/:bookId', readerController.getReadingListsWithBookStatus);
router.get('/reading-lists/:id', readerController.getReadingList);
router.put('/reading-lists/:id', readerController.updateReadingList);
router.delete('/reading-lists/:id', readerController.deleteReadingList);

// Routes API pour les entrées de liste
router.post('/reading-lists/add-book', readerController.addBookToList);
router.delete('/reading-lists/:listId/books/:bookId', readerController.removeBookFromList);

// Routes API pour les favoris
router.get('/favorites', readerController.getFavorites);
router.get('/favorites/check/:bookId', readerController.checkFavoriteStatus);
router.post('/favorites', readerController.addFavorite);
router.delete('/favorites/:bookId', readerController.removeFavorite);

export default router;
