/* Styles pour les listes de lecture */

/* Modal d'ajout à une liste */
.reading-lists-select {
    max-height: 400px;
    overflow-y: auto;
}

.list-select-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 8px;
    background-color: #fff;
    transition: all 0.2s ease;
}

.list-select-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.list-select-item > div {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.list-select-item span {
    font-weight: 500;
    color: #333;
}

.list-book-count {
    font-size: 0.875rem;
    color: #666;
    font-weight: 400 !important;
}

/* Boutons dans la modal */
.list-select-item button {
    min-width: 120px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: center;
}

.list-select-item button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

.list-select-item button:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Bouton "Retirer" */
.list-select-item .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.list-select-item .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Animation de chargement */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .list-select-item {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .list-select-item button {
        width: 100%;
    }
}
