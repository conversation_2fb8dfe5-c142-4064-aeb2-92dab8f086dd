
import express from 'express';
import readerController from '../controllers/readerController.js';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// Middleware pour toutes les routes de lecteur
router.use(isAuthenticated);

// Routes pour les listes de lecture (vues)
router.get('/reading-lists', readerController.getReadingLists);
router.get('/reading-lists/new', readerController.newReadingListForm);
router.post('/reading-lists', readerController.createReadingList);
router.get('/reading-lists/:id', readerController.getReadingListDetails);
router.get('/reading-lists/edit/:id', readerController.editReadingListForm);
router.post('/reading-lists/edit/:id', readerController.updateReadingList);
router.post('/reading-lists/delete/:id', readerController.deleteReadingList);

// Routes pour les entrées de liste
router.post('/reading-lists/add-book', readerController.addBookToList);
router.delete('/reading-lists/:listId/books/:bookId', readerController.removeBookFromList);

// Routes pour les favoris
router.get('/favorites', readerController.getFavorites);
router.get('/favorites/check/:bookId', readerController.checkFavoriteStatus);
router.post('/favorites', readerController.addFavorite);
router.delete('/favorites/:bookId', readerController.removeFavorite);

export default router;



